from fastapi import Request, HTTPException
from twilio.request_validator import <PERSON><PERSON><PERSON><PERSON>da<PERSON>
from sqlalchemy.orm import Session
from sqlalchemy import text
from app.config import settings
from app.utils.whatsapp_handler import send_whatsapp_message

async def is_valid_twilio_request(request: Request) -> bool:
    """
    Validates that the request came from <PERSON><PERSON><PERSON> by checking its signature.
    """
    validator = RequestValidator(settings.TWILIO_AUTH_TOKEN)
    print(settings.TWILIO_AUTH_TOKEN)
    signature = request.headers.get("X-Twilio-Signature", "")

    # Correct the URL by forcing HTTPS
    correct_url = str(request.url).replace("http://", "https://")

    form_data = await request.form()

    # Log expected URL vs. actual URL
    print(f"🔍 Twilio Sent Request To: {request.url}")
    print(f"🔄 Adjusted URL for Validation: {correct_url}")

    return validator.validate(correct_url, dict(form_data), signature)


def get_user_by_phone(phone_number: str, db: Session):
    """
    Checks if a user with the given phone number exists in the `crm.user` table
    and is allowed to use WhatsApp.
    """
    query = text(f"""
        SELECT * FROM {settings.SCHEMA}.user
        WHERE regexp_replace(phone_number, '\s+', '', 'g') = :phone_number
        AND use_whatsapp = TRUE
        LIMIT 1
    """)
    result = db.execute(query, {"phone_number": phone_number}).fetchone()
    return result


async def validate_whatsapp_user(request: Request, phone_number: str, db: Session):
    """
    1️⃣ Checks if the request is from Twilio.
    2️⃣ Verifies if the phone number exists and is allowed to use WhatsApp.
    """
    # 1️⃣ Verify Twilio Request
    if not await is_valid_twilio_request(request):
        send_whatsapp_message(phone_number, "Unauthorized: Invalid Twilio request")
        raise HTTPException(status_code=403, detail="Unauthorized: Invalid Twilio request")

    # 2️⃣ Check User in Database
    user = get_user_by_phone(phone_number, db)
    if not user:
        send_whatsapp_message(phone_number, "Unauthorized: This phone number is not registered for WhatsApp use")
        raise HTTPException(status_code=403, detail="Unauthorized: This phone number is not registered for WhatsApp use")

    return user
