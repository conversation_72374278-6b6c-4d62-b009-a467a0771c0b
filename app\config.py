import os
from pydantic_settings import BaseSettings
from dotenv import load_dotenv

load_dotenv()

class Settings(BaseSettings):
    # General settings
    PROJECT_NAME: str = "WhatsApp Bot Service"
    VERSION: str = "1.0.0"
    DEBUG: bool = True

    # Twilio API settings
    TWILIO_ACCOUNT_SID: str = os.getenv("TWILIO_ACCOUNT_SID")
    TWILIO_AUTH_TOKEN: str = os.getenv("TWILIO_AUTH_TOKEN")
    TWILIO_PHONE_NUMBER: str = os.getenv("TWILIO_PHONE_NUMBER")

    # OpenAI API settings
    OPENAI_API_KEY: str = os.getenv("OPENAI_API_KEY")
    ASSISTANT_ID: str = os.getenv("ASSISTANT_ID")
    ASSISTANT_PLAYER_ID: str = os.getenv("ASSISTANT_PLAYER_ID")

    # Database settings
    PG_URL_PROD: str = os.getenv("PG_URL_PROD")
    SCHEMA: str = os.getenv("SCHEMA")

    # Lookup API (For team name lookup)
    LOOKUP_API_URL: str = os.getenv("LOOKUP_API_URL")
    LOOKUP_API_USR: str = os.getenv("LOOKUP_API_USR")
    LOOKUP_API_PASS: str = os.getenv("LOOKUP_API_PASS")

    # Deployment settings
    HOST: str = os.getenv("HOST", "0.0.0.0")
    PORT: int = int(os.getenv("PORT", 8080))

    # Platform settings
    PLATFORM_URL: str = os.getenv("PLATFORM_URL")

    class Config:
        case_sensitive = True

# Create a settings instance
settings = Settings()
