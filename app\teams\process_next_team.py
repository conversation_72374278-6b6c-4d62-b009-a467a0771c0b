
import json
from app.utils.whatsapp_handler import (
    send_whatsapp_message,
    send_button_whatsapp_message,
    format_whatsapp_message,
)

# Function to process the next team in the queue
def process_next_team(sender, user_sessions):
    """
    Processes the next team request in the queue for a user.
    Groups positions for display, but stores original JSON entries separately.
    """
    if sender not in user_sessions or not user_sessions[sender]["pending_teams"]:
        del user_sessions[sender]  # Clear session
        return
    # Get the next team from the queue
    next_team_name = user_sessions[sender]["pending_teams"][0]["team_name"]
    user_sessions[sender]["current_team"] = next_team_name

    # ✅ Store original requests separately (No grouping in storage)
    team_requests = [entry for entry in user_sessions[sender]["pending_teams"] if entry["team_name"] == next_team_name]

    # ✅ Group positions for user message
    grouped_display = {
        "team_name": next_team_name,
        "positions": [],
        "transfer_fee": set(),
        "asking_salary": set(),
        "foot": None,
        "type": [],
        "max_age": None,
        "eu_passport": None,
        "description": "",
        "teamId": None,
        "contact_person": None  # Add contact person field
    }

    to_remove = []  # Track processed requests

    for entry in team_requests:
        # ✅ Add only to user message display
        if "position" in entry:
            grouped_display["positions"].append(entry["position"])
        if "transfer_fee" in entry:
            grouped_display["transfer_fee"].add(entry["transfer_fee"])
        if "asking_salary" in entry:
            grouped_display["asking_salary"].add(entry["asking_salary"])

        grouped_display["foot"] = entry.get("foot", grouped_display["foot"])
        grouped_display["type"] = entry.get("type", grouped_display["type"])
        grouped_display["max_age"] = entry.get("max_age", grouped_display["max_age"])
        grouped_display["eu_passport"] = entry.get("eu_passport", grouped_display["eu_passport"])
        grouped_display["description"] += f"\n- {entry.get('description', '')}"
        grouped_display["teamId"] = entry.get("teamId", grouped_display["teamId"])
        
        # Set contact person if available
        if entry.get("contact_person") and not grouped_display["contact_person"]:
            grouped_display["contact_person"] = entry["contact_person"]

        to_remove.append(entry)
    # ✅ Remove processed team requests from queue
    for item in to_remove:
        user_sessions[sender]["pending_teams"].remove(item)

    # ✅ Store original request data for later processing
    if "stored_requests" not in user_sessions[sender]:
        user_sessions[sender]["stored_requests"] = []
    
    user_sessions[sender]["stored_requests"].extend(team_requests)

    # ✅ Format response for WhatsApp (positions grouped)
    formatted_message = format_whatsapp_message({"result": [grouped_display]})

    # ✅ Store lookup options for this team
    if next_team_name in user_sessions[sender]["team_name_updates"]:
        sorted_teams = user_sessions[sender]["team_name_updates"][next_team_name]["top_teams"][:5]
        user_sessions[sender]["lookup_options"][next_team_name] = sorted_teams

    # ✅ Send the formatted message with grouped positions
    send_whatsapp_message(sender, formatted_message)

    # ✅ Ask user for confirmation or change
    send_button_whatsapp_message(sender, "HX95644dd9f0c44ae27546e075c5338e91")
