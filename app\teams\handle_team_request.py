from app.config import settings
import time
from app.utils import get_async_response
from app.utils.whatsapp_handler import (
    send_whatsapp_message,
)
import json
from .process_next_team import process_next_team
import requests
import asyncio
from app.db import insert_whatsapp_request
import time
import logging

logging.basicConfig(level=logging.INFO)


async def handle_team_request(sender, incoming_msg, client, user_sessions):
    """
    Handles team-related requests.
    """
    start_time = time.perf_counter()
    send_whatsapp_message(sender, "Processing your request, please wait...")

    try:
        insert_whatsapp_request(sender, incoming_msg)
    except Exception as e:
        print("Error saving the player log", e)

    # ✅ Step 1: Create OpenAI Assistant Thread
    thread_start = time.perf_counter()
    thread = client.beta.threads.create()
    thread_id = thread.id
    logging.info(
        f"📌 Created OpenAI thread in {time.perf_counter() - thread_start:.4f} sec"
    )

    # ✅ Step 2: Send message to OpenAI
    send_msg_start = time.perf_counter()
    client.beta.threads.messages.create(
        thread_id=thread_id, role="user", content=incoming_msg
    )
    logging.info(
        f"✉️ Sent message to OpenAI in {time.perf_counter() - send_msg_start:.4f} sec"
    )

    # ✅ Step 3: Run Assistant and Wait
    run_start = time.perf_counter()
    run = client.beta.threads.runs.create(
        thread_id=thread_id, assistant_id=settings.ASSISTANT_ID
    )

    # Define terminal states that should break the polling loop
    terminal_states = ["completed", "failed", "cancelled", "expired", "incomplete"]

    while True:
        run_status = client.beta.threads.runs.retrieve(
            thread_id=thread_id, run_id=run.id
        )
        if run_status.status in terminal_states:
            if run_status.status != "completed":
                logging.error(f"Run ended with non-success status: {run_status.status}")
                send_whatsapp_message(
                    sender, "⚠️ Sorry, I encountered an error processing your request. Please try again."
                )
                return  # Exit the function early
            break
        await asyncio.sleep(1)

    logging.info(f"🕒 OpenAI processing took {time.perf_counter() - run_start:.4f} sec")

    # ✅ Step 4: Retrieve Messages
    messages_start = time.perf_counter()
    messages = list(client.beta.threads.messages.list(thread_id=thread_id))
    logging.info(
        f"📨 Retrieved OpenAI messages in {time.perf_counter() - messages_start:.4f} sec"
    )

    ai_reply = None
    for msg in reversed(messages):
        if msg.role == "assistant":
            try:
                ai_reply = json.loads(msg.content[0].text.value.strip())
            except Exception as e:
                ai_reply = {"error": f"Invalid AI response format: {e}"}
            break

    if not ai_reply or isinstance(ai_reply, str):
        ai_reply = {
            "error": "Sorry, I couldn't process your request. Please try again."
        }

    if not ai_reply.get("result"):
        send_whatsapp_message(
            sender,
            "⚠️ Sorry, I couldn't find any teams in the message. Please try again.",
        )
        return

    # ✅ Step 5: Lookup API Call
    lookup_start = time.perf_counter()
    team_name_updates = {}
    filtered_sorted_teams = []
    processed_teams = {}
    error_occurred = False
    for entry in ai_reply.get("result", []):
        team_name = entry["team_name"]
        if team_name in processed_teams:
            logging.info(f"⚡ Skipping API lookup for {team_name}, using cached data.")
            entry["team_name"] = processed_teams[team_name]["new_team_name"]
            entry["teamId"] = processed_teams[team_name]["teamId"]
            continue  # Move to the next entry

        try:
            api_start = time.perf_counter()
            resp = await get_async_response(
                f"{settings.LOOKUP_API_URL}/teams/lookup/{team_name}",
                auth=(settings.LOOKUP_API_USR, settings.LOOKUP_API_PASS),
            )
            logging.info(
                f"🌍 API lookup for {team_name} took {time.perf_counter() - api_start:.4f} sec"
            )

            if resp.is_error:
                send_whatsapp_message(
                    sender,
                    f"⚠️ Sorry, I couldn't find any matching teams for '{team_name}'. ",
                )
                error_occurred = True
                break
            else:
                lookup_results = resp.json()
                filtered_sorted_teams = sorted(
                    [
                        team
                        for team in lookup_results
                        if team["similarity_score"] <= 0.75
                    ],
                    key=lambda x: x["smoothed_rating"],
                    reverse=True,
                )
                if filtered_sorted_teams:
                    best_match = filtered_sorted_teams[0]
                    processed_teams[team_name] = {
                        "new_team_name": best_match["name"],
                        "teamId": best_match["teamId"],
                        "top_teams": filtered_sorted_teams[1:6],
                    }

                    team_name_updates[team_name] = processed_teams[team_name]

                    entry["team_name"] = best_match["name"]
                    entry["teamId"] = best_match["teamId"]
                else:
                    send_whatsapp_message(
                        sender,
                        f"⚠️ Sorry, I couldn't find any matching teams for '{team_name}'. ",
                    )
                    error_occurred = True
                    break

        except requests.RequestException as e:
            send_whatsapp_message(sender, f"❌ Failed to fetch lookup for {team_name}")
            logging.error(f"❌ Failed to fetch lookup for {team_name}: {str(e)}")
            error_occurred = True
            break

    logging.info(
        f"🔍 Team lookup and processing took {time.perf_counter() - lookup_start:.4f} sec"
    )

    if error_occurred:
        return
    # ✅ Step 6: Update User Session
    session_update_start = time.perf_counter()
    user_sessions[sender]["json_data"] = ai_reply
    user_sessions[sender]["status"] = "awaiting_confirmation"
    user_sessions[sender]["pending_teams"] = ai_reply.get("result", [])
    user_sessions[sender]["team_name_updates"] = team_name_updates
    user_sessions[sender]["lookup_options"] = {}

    # Extract contact person information if available
    for team in user_sessions[sender]["pending_teams"]:
        team_name = team["team_name"]
        # Add contact_person field if it exists in AI reply
        if "contact_person" in team:
            logging.info(f"Contact person found for {team_name}: {team['contact_person']}")
        else:
            team["contact_person"] = None
            
        if team_name in team_name_updates:
            team["team_name"] = team_name_updates[team_name]["new_team_name"]
            team["teamId"] = team_name_updates[team_name]["teamId"]

    logging.info(
        f"🗂 User session update took {time.perf_counter() - session_update_start:.4f} sec"
    )

    # ✅ Step 7: Process Next Team
    process_start = time.perf_counter()
    process_next_team(sender, user_sessions)
    logging.info(
        f"🚀 process_next_team took {time.perf_counter() - process_start:.4f} sec"
    )

    # ✅ Final Logging
    logging.info(
        f"✅ Total handle_team_request execution time: {time.perf_counter() - start_time:.4f} sec"
    )
