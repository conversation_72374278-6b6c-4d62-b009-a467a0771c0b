import re
from app.utils.whatsapp_handler import (
    send_whatsapp_message,
)
import requests
import json
import time
from app.config import settings
from app.db import insert_whatsapp_request

async def process_player_request(
    sender: str, message: str, client, user_sessions: dict
):
    """Handle player request processing using OpenAI assistant"""
    try:
        # Extract Transfermarkt URL
        transfermarkt_match = re.search(
            r"https?://(www\.)?transfermarkt\.(com|com.ar|co.uk|de|it|ar|co|fr|es|nl|ru|pt|pl|tr|gr|cz|ro|hu|ua|br|ar|mx|ch|at|dk|se|no|fi|be|hr|rs|bg|sk|si|ba|ie|lu|mt|cy|lt|lv|ee|us)/.*/spieler/\d+",
            message,
        )
        if not transfermarkt_match:
            send_whatsapp_message(
                sender, "⚠ Please include a valid Transfermarkt player profile link."
            )
            return

        send_whatsapp_message(sender, "Processing your player, please wait...")
        try:
            insert_whatsapp_request(sender, message)
        except Exception as e:
            print("Error saving the player log", e)
        transfermarkt_url = transfermarkt_match.group()

        # Create thread and run assistant
        thread = client.beta.threads.create()
        client.beta.threads.messages.create(
            thread_id=thread.id, role="user", content=message
        )

        run = client.beta.threads.runs.create(
            thread_id=thread.id, assistant_id=settings.ASSISTANT_PLAYER_ID
        )

        # Wait for assistant completion
        while True:
            run_status = client.beta.threads.runs.retrieve(
                thread_id=thread.id, run_id=run.id
            )
            if run_status.status == "completed":
                break
            time.sleep(1)

        # Get assistant response
        messages = list(client.beta.threads.messages.list(thread_id=thread.id))
        assistant_response = None
        for msg in reversed(messages):
            if msg.role == "assistant":
                try:
                    answer = msg.content[0].text.value.strip()
                    if "json" in answer:
                        assistant_response = json.loads(
                            re.sub(r"```(\w+)?", "", answer).strip()
                        )
                    else:
                        assistant_response = json.loads(answer)
                except json.JSONDecodeError as e:
                    print(e)
                    continue
        if not assistant_response or isinstance(assistant_response, str):
            send_whatsapp_message(
                sender,
                "⚠ Could not process player data. Please ensure the message contains a valid Transfermarkt link and relevant details.",
            )
            return

        # Store parsed data in session
        user_sessions[sender] = {
            "type": "player",
            "data": {
                "transfermarkt_link": transfermarkt_url,
                "transfer_fee": assistant_response.get("transfer_fee"),
                "expected_salary": assistant_response.get("expected_salary"),
                "video_link": assistant_response.get("video_link"),
                "description": assistant_response.get("description"),
                "control_stage": assistant_response.get("control_stage"),  # Add control stage
            },
        }
        # Build confirmation message
        confirmation_msg = "📝 *Player Request Details:*\n"
        if assistant_response.get("expected_salary"):
            confirmation_msg += (
                f"💰 Asking Salary: {assistant_response['expected_salary']}\n"
            )
        if assistant_response.get("transfer_fee"):
            confirmation_msg += (
                f"📤 Transfer Fee: {assistant_response['transfer_fee']}\n"
            )
        if assistant_response.get("video_link"):
            confirmation_msg += f"🎥 Video Link: {assistant_response['video_link']}\n"
        if assistant_response.get("control_stage"):
            confirmation_msg += f"🎮 Control Stage: {assistant_response['control_stage']}\n"

        await handle_player_creation(
            sender, user_sessions[sender]["data"], user_sessions
        )

    except Exception as e:
        send_whatsapp_message(
            sender, "⚠ Error processing player request. Please try again."
        )
        print(f"Player processing error: {str(e)}")


async def handle_player_creation(sender: str, player_data: dict, user_sessions: dict):
    """Handle final player submission"""

    phone_number = sender.split(":")[1]

    if player_data["transfer_fee"] == "null":
        player_data["transfer_fee"] = None
    if player_data["expected_salary"] == "null":
        player_data["expected_salary"] = None
    if player_data["video_link"] == "null":
        player_data["video_link"] = None
    if player_data["description"] == "null":
        player_data["description"] = None
    if player_data.get("control_stage") == "null":
        player_data["control_stage"] = None
    try:
        # Prepare payload for API
        payload = {
            "club_asking_price": player_data.get("transfer_fee"),
            "expected_net_salary": player_data.get("expected_salary"),
            "video_link": player_data.get("video_link"),
            "description": player_data.get("description"),
            "control_stage": player_data.get("control_stage"),  # Add control stage to payload
        }
        splitted_tm_link = player_data["transfermarkt_link"].split("/")
        tm_player_id = (
            splitted_tm_link[-1] if splitted_tm_link[-1] else splitted_tm_link[-2]
        )
        payload["tm_player_id"] = tm_player_id
        # Make API call
        response = requests.post(
            f"{settings.PLATFORM_URL}/player_records/whatsapp?phone_number={phone_number}",
            json=payload,
            headers={"Content-Type": "application/json"},
        )
        # Handle response
        error_data = response.json()
        if response.status_code == 200:
            send_whatsapp_message(sender, f"✅ Player record created successfully! Tokens left: {error_data['tokens_left']}")
        elif response.status_code == 409:
            if error_data.get("detail") == "tm_data_not_processed":
                send_whatsapp_message(
                    sender, "⚠ Transfermarkt data hasn't been processed yet."
                )
            elif error_data.get("detail") == "player_already_exists":
                send_whatsapp_message(
                    sender, "⚠ This player already exists in our system."
                )
        elif response.status_code == 402:
            tokens_left = error_data.get("tokens_left", 0)
            send_whatsapp_message(
                sender,
                f"⚠ Not enough tokens to create player record! Tokens left: {tokens_left}",
            )
        else:
            send_whatsapp_message(
                sender, "⚠ Error submitting player record. Please try again."
            )

        # Clear session regardless of outcome
        del user_sessions[sender]

    except requests.RequestException as e:
        send_whatsapp_message(sender, "⚠ Connection error. Please try again later.")
        print(f"API Connection Error: {str(e)}")
    except Exception as e:
        send_whatsapp_message(sender, "⚠ Unexpected error occurred.")
        print(f"Submission Error: {str(e)}")
