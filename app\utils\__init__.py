import httpx

async def get_async_response(url, auth, params = None):
    async with httpx.AsyncClient() as client:
        # Make the asynchronous request to the external API
        response = await client.get(url,
            auth=auth,
            params=params, timeout=None)
        return response

def get_active_window(date):
    year = date.year
    current_month = date.month
    
    if 0 <= current_month <= 2:
        return f"winter_{year}"
    elif 3 <= current_month <= 8:
        return f"summer_{year}"
    return f"winter_{year + 1}"

def human_readable_number(num):
    if num >= 1_000_000:
        return f"{num // 1_000_000}m"
    elif num >= 1_000:
        return f"{num // 1_000}k"
    return str(num)

def ordinal_superscript(n):
    suffixes = {1: "ˢᵗ", 2: "ⁿᵈ", 3: "ʳᵈ"}
    if 10 <= n % 100 <= 20:
        suffix = "ᵗʰ"
    else:
        suffix = suffixes.get(n % 10, "ᵗʰ")
    return f"{n}{suffix}"