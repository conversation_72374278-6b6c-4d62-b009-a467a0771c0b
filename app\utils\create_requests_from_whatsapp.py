import requests
from app.config import settings
from app.utils.whatsapp_handler import send_whatsapp_message
from app.utils import get_active_window
from datetime import datetime

async def create_request_from_whatsapp(sender, phone_number, team_request_payload):
    headers = {"Content-Type": "application/json"}

    # ✅ Send request to the team request API
    for request in team_request_payload:
        request["position"] = request["position"].lower()
        request["max_value"] = request["transfer_fee"]
        request["max_net_salary"] = request["asking_salary"]
        if not request["foot"]:
            request["foot"] = "no_preference"
        request["transfer_period"] = [get_active_window(datetime.now())]
        
        # Include contact person in the payload if available
        if "contact_person" in request and request["contact_person"]:
            request["club_contact"] = request["contact_person"]
        else:
            request["club_contact"] = None
    try:
        response = requests.post(
            f"{settings.PLATFORM_URL}/team_requests/whatsapp/bulk?phone_number={phone_number}",
            json=team_request_payload,
            headers=headers,
        )
        response_data = response.json()
        if response.status_code == 200:

            send_whatsapp_message(sender, f"✅ Team request successfully created! Tokens left: {response_data['tokens_left']}")
            pass
        elif response.status_code == 402:
            tokens_left = response_data.get("tokens_left", 0)
            send_whatsapp_message(
                sender,
                f"⚠ Not enough tokens to create team request! Tokens left: {tokens_left}",
            )
            pass
        else:
            send_whatsapp_message(
                sender, f"⚠ Failed to create team request: {response_data}"
            )

    except requests.RequestException as e:
        print(str(e))
