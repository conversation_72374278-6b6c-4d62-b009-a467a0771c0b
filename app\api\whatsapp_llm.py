from fastapi import APIRouter, Request, Depends
from fastapi.responses import PlainTextResponse
import os
import logging
from openai import OpenAI
from app.utils.whatsapp_handler import send_whatsapp_message
from app.utils.validate_phone import validate_whatsapp_user
from app.utils import ordinal_superscript
from sqlalchemy.orm import Session
from app.db import get_db
from app.utils.create_requests_from_whatsapp import create_request_from_whatsapp
from app.utils.handle_twillio_media import handle_whatsapp_media
from app.teams.process_next_team import process_next_team
from app.teams.handle_team_request import handle_team_request
from app.players.handle_player_request import process_player_request
from app.utils.is_player_request import is_player_request
from app.utils.message_buffer import add_message_to_buffer 

# Configure logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

# OpenAI Client
client = OpenAI(api_key=os.environ.get("OPENAI_API_KEY"))

# FastAPI Router
router = APIRouter()

# Store user interaction state
user_sessions = {}

@router.post("/whatsapp-webhook", response_class=PlainTextResponse)
async def whatsapp_webhook(request: Request, db: Session = Depends(get_db)):
    """
    Handles incoming WhatsApp messages via Twilio.
    Sends user messages to OpenAI Assistant and returns the response.
    """
    form_data = await request.form()
    incoming_msg = form_data.get("Body", "").strip()
    sender = form_data.get("From", "").strip()
    
    logger.info(f"Incoming message from {sender}: {incoming_msg}")

    phone_number = sender.split(":")[1]
    await validate_whatsapp_user(request, phone_number, db)

    num_media = int(form_data.get("NumMedia", 0))
    if num_media > 0:
        media_type = form_data.get("MediaContentType0", "")
        media_url = form_data.get("MediaUrl0", "")

        logger.info(f"Media detected from {sender}: {media_type} - {media_url}")
        
        if media_url:
            incoming_msg = await handle_whatsapp_media(sender, media_url, media_type)
            if not incoming_msg:
                logger.warning(f"Failed to process media from {sender}")
                return None
    
    await add_message_to_buffer(sender, incoming_msg, process_final_message)
    return None
    
async def process_final_message(sender, merged_message):
    """
    Processes the final merged message after the buffer delay.
    """
    logger.info(f"Processing final message from {sender}: {merged_message}")
    
    if sender not in user_sessions:
        user_sessions[sender] = {}
    
    phone_number = sender.split(":")[1]

    if sender in user_sessions and user_sessions[sender].get("awaiting_team_selection"):
        logger.info(f"Handling team selection flow for {sender}")
        if merged_message.isdigit():
            selected_index = int(merged_message) - 1
            current_team_name = user_sessions[sender]["current_team"]
            original_team_name = next(
                (orig_name for orig_name, data in user_sessions[sender]["team_name_updates"].items()
                if data.get("new_team_name") == current_team_name),
                current_team_name
            )
            lookup_options = user_sessions[sender]["team_name_updates"].get(original_team_name, {}).get("top_teams", [])
            
            if 0 <= selected_index < len(lookup_options):
                chosen_team = lookup_options[selected_index]
                new_team_name = chosen_team["name"]
                new_team_id = chosen_team["teamId"]
                
                user_sessions[sender]["team_name_updates"][original_team_name]["new_team_name"] = new_team_name
                user_sessions[sender]["team_name_updates"][original_team_name]["teamId"] = new_team_id
                user_sessions[sender]["awaiting_team_selection"] = False
                
                for entry in user_sessions[sender]["stored_requests"]:
                    if entry["team_name"] == current_team_name:
                        entry["team_name"] = new_team_name
                        entry["teamId"] = new_team_id

                updated_team_request_payload = [
                    entry for entry in user_sessions[sender]["stored_requests"] if entry["team_name"] == new_team_name
                ]
                response = await create_request_from_whatsapp(sender, phone_number, updated_team_request_payload)
                process_next_team(sender, user_sessions)
                return None
            else:
                logger.warning(f"Invalid team selection from {sender}: {merged_message}")
                send_whatsapp_message(sender, "⚠ Invalid selection. Please enter a number between 1-5.")
                return None

    if sender in user_sessions and user_sessions[sender].get("status") == "awaiting_confirmation":
        logger.info(f"Handling confirmation flow for {sender}")
        if "proceed" in merged_message.lower():
            current_team_name = user_sessions[sender]["current_team"]
            current_team_requests = [
                entry for entry in user_sessions[sender]["stored_requests"]
                if entry["team_name"] == current_team_name
            ]
            response = await create_request_from_whatsapp(sender, phone_number, current_team_requests)
            process_next_team(sender, user_sessions)
            return None
        elif "change" in merged_message.lower():
            logger.info(f"{sender} requested to change team selection")
            if "current_team" not in user_sessions[sender] or not user_sessions[sender]["current_team"]:
                send_whatsapp_message(sender, "⚠ No team is currently being reviewed.")
                return None

            current_team_name = user_sessions[sender]["current_team"]
            original_team_name = next(
                (orig_name for orig_name, data in user_sessions[sender]["team_name_updates"].items()
                if data.get("new_team_name") == current_team_name),
                current_team_name
            )
            team_name_updates = user_sessions[sender].get("team_name_updates", {})
            lookup_options = team_name_updates.get(original_team_name, {}).get("top_teams", [])

            if not lookup_options:
                send_whatsapp_message(sender, f"⚠ No alternative teams found for {current_team_name}.")
                return None

            formatted_options = "\n".join(
                [f"{i} - {team['name']} ({team['area_name']}, {ordinal_superscript(team['division_level'])} division)" for i, team in enumerate(lookup_options, start=1)]
            )
            send_whatsapp_message(
                sender,
                f"🔄 *Top 5 Alternative Teams for {current_team_name}:*\n{formatted_options}\n\n"
                "Reply with a number (1-5) to select an alternative team."
            )
            user_sessions[sender]["awaiting_team_selection"] = True
            return None

    if is_player_request(merged_message):
        logger.info(f"Processing player request from {sender}")
        await process_player_request(sender, merged_message, client, user_sessions)
    else:
        logger.info(f"Processing team request from {sender}")
        await handle_team_request(sender, merged_message, client, user_sessions)
