from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from app.api.whatsapp_llm import router
from app.config import settings
from app.db import Base, engine

app = FastAPI(
    title=settings.PROJECT_NAME,
    version=settings.VERSION,
    description="A FastAPI service for processing WhatsApp messages via Twilio and OpenAI/Gemini."
)

Base.metadata.create_all(bind=engine)

# CORS settings (if needed)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Adjust this for security (e.g., allow only Twilio)
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include WhatsApp webhook router
app.include_router(router, prefix="", tags=["whatsapp"])

@app.get("/")
async def root():
    return {"message": "WhatsApp Bot API is running!"}
