import aiohttp
import os
from openai import OpenAI
from app.utils.whatsapp_handler import send_whatsapp_message
import pdfplumber
import pytesseract
import aiohttp
from PIL import Image
import io

# Initialize OpenAI client
client = OpenAI(api_key=os.environ.get("OPENAI_API_KEY"))

async def download_twilio_media(media_url: str) -> bytes:
    """
    Downloads media (audio, documents, images, etc.) from Twilio using authentication.
    """
    try:
        # Twilio credentials
        TWILIO_ACCOUNT_SID = os.getenv("TWILIO_ACCOUNT_SID")
        TWILIO_AUTH_TOKEN = os.getenv("TWILIO_AUTH_TOKEN")

        auth = aiohttp.BasicAuth(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN)

        async with aiohttp.ClientSession() as session:
            async with session.get(media_url, auth=auth) as response:
                print(f"Twilio Media Response: {response.status}")

                if response.status == 200:
                    return await response.read()  # Return file bytes
                else:
                    print(f"Failed to download media. Status: {response.status}")
                    return None

    except Exception as e:
        print(f"Error downloading media: {e}")
        return None

async def extract_text_from_image(file_data: bytes) -> str:
    """
    Extracts text from an image using Tesseract OCR.
    """
    try:
        image = Image.open(io.BytesIO(file_data))  # Convert bytes to image
        extracted_text = pytesseract.image_to_string(image)  # OCR extraction

        return extracted_text.strip() if extracted_text else "⚠ No readable text found in the image."

    except Exception as e:
        print(f"Error extracting text from image: {e}")
        return None

async def extract_text_from_pdf(file_data: bytes) -> str:
    """
    Extracts text from a PDF file using pdfplumber.
    """
    try:
        with open("temp.pdf", "wb") as f:
            f.write(file_data)  # Save PDF temporarily

        with pdfplumber.open("temp.pdf") as pdf:
            text = "\n".join([page.extract_text() for page in pdf.pages if page.extract_text()])

        return text.strip() if text else "⚠ No extractable text found in the PDF."

    except Exception as e:
        print(f"Error extracting text from PDF: {e}")
        return None

async def handle_whatsapp_media(sender: str, media_url: str, media_type: str):
    """
    Handles different types of media (audio, PDF, images) from WhatsApp.
    """

    # Step 1: Download the file
    file_data = await download_twilio_media(media_url)
    if not file_data:
        send_whatsapp_message(sender, "⚠ Failed to download the file. Please try again.")
        return

    # Step 2: Handle Audio (Transcription)
    if "audio" in media_type:
        transcribed_text = client.audio.transcriptions.create(
            model="whisper-1",
            file=("voice_message.mp3", file_data, "audio/mpeg"),
            response_format="text"
        )
        if transcribed_text:
            return transcribed_text
        else:
            send_whatsapp_message(sender, "⚠ Failed to process voice message.")
            return None

    # Step 3: Handle PDF (Extract text using OpenAI)
    extracted_text = None
    if "pdf" in media_type:
        extracted_text = await extract_text_from_pdf(file_data)
        

    # Step 4: Handle Images (Optionally, extract text with OCR)
    if "image" in media_type:
        extracted_text = await extract_text_from_image(file_data)
        
    if extracted_text:
        return extracted_text
    else:
        send_whatsapp_message(sender, "⚠ Failed to extract text from the document.")
        return None
